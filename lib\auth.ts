import { supabase } from './supabase'
import { User } from '@supabase/supabase-js'

export interface AuthUser extends User {
  profile?: {
    full_name: string | null
    email: string | null
  }
}

// Cache for user profile to avoid repeated database calls
let profileCache: { [userId: string]: { profile: any, timestamp: number } } = {}
const PROFILE_CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export const auth = {
  // Sign up with email and password
  signUp: async (email: string, password: string, fullName?: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName || '',
        },
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    })
    return { data, error }
  },

  // Sign in with email and password
  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { data, error }
  },

  // Sign out
  signOut: async () => {
    // Clear profile cache on sign out
    profileCache = {}
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  // Get current user with improved caching and error handling
  getCurrentUser: async (): Promise<AuthUser | null> => {
    try {
      // First check if we have a valid session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()

      if (sessionError) {
        console.error('Error getting session:', sessionError)
        return null
      }

      if (!session?.user) {
        return null
      }

      const user = session.user

      // Check cache first
      const cached = profileCache[user.id]
      const now = Date.now()

      if (cached && (now - cached.timestamp) < PROFILE_CACHE_DURATION) {
        return {
          ...user,
          profile: cached.profile
        }
      }

      // Fetch user profile with timeout and retry logic
      let profile = null
      try {
        const { data: profileData, error: profileError } = await Promise.race([
          supabase
            .from('profiles')
            .select('full_name, email')
            .eq('id', user.id)
            .single(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Profile fetch timeout')), 5000)
          )
        ]) as any

        if (profileError) {
          console.warn('Error fetching profile:', profileError)
          profile = { full_name: null, email: null }
        } else {
          profile = profileData || { full_name: null, email: null }
        }
      } catch (error) {
        console.warn('Profile fetch failed, using defaults:', error)
        profile = { full_name: null, email: null }
      }

      // Cache the profile
      profileCache[user.id] = {
        profile,
        timestamp: now
      }

      return {
        ...user,
        profile
      }
    } catch (error) {
      console.error('Unexpected error in getCurrentUser:', error)
      return null
    }
  },

  // Update user profile
  updateProfile: async (updates: { full_name?: string; email?: string }) => {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) throw new Error('No user logged in')

    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', user.id)
      .select()
      .single()

    // Clear cache for this user to force refresh
    if (profileCache[user.id]) {
      delete profileCache[user.id]
    }

    return { data, error }
  },

  // Reset password
  resetPassword: async (email: string) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/callback?next=/reset-password`,
    })
    return { data, error }
  },

  // Update password
  updatePassword: async (password: string) => {
    const { data, error } = await supabase.auth.updateUser({
      password,
    })
    return { data, error }
  },

  // Listen to auth state changes with improved handling
  onAuthStateChange: (callback: (user: AuthUser | null) => void) => {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔐 Auth event:', event, 'Session:', !!session)

      try {
        if (event === 'SIGNED_OUT' || !session?.user) {
          // Clear cache on sign out
          profileCache = {}
          callback(null)
          return
        }

        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          // For sign in and token refresh, get the full user with profile
          const authUser = await auth.getCurrentUser()
          callback(authUser)
          return
        }

        // For other events, use the session user directly to avoid extra calls
        if (session?.user) {
          // Check if we have cached profile data
          const cached = profileCache[session.user.id]
          if (cached && (Date.now() - cached.timestamp) < PROFILE_CACHE_DURATION) {
            callback({
              ...session.user,
              profile: cached.profile
            })
          } else {
            // Fallback to full user fetch
            const authUser = await auth.getCurrentUser()
            callback(authUser)
          }
        } else {
          callback(null)
        }
      } catch (error) {
        console.error('Error in auth state change handler:', error)
        callback(null)
      }
    })
  },

  // Clear profile cache (useful for debugging)
  clearCache: () => {
    profileCache = {}
  },

  // Get current session without profile data (faster)
  getSession: async () => {
    return await supabase.auth.getSession()
  }
}
