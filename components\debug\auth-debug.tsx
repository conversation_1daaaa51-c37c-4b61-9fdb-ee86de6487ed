'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth/auth-provider'
import { auth } from '@/lib/auth'
import { supabase } from '@/lib/supabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { RefreshCw, Bug, AlertTriangle, CheckCircle } from 'lucide-react'

interface AuthDebugProps {
  show?: boolean
}

export function AuthDebug({ show = false }: AuthDebugProps) {
  const { user, loading } = useAuth()
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const [sessionInfo, setSessionInfo] = useState<any>(null)
  const [isVisible, setIsVisible] = useState(show)

  useEffect(() => {
    // Show debug panel with Ctrl+Shift+D
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault()
        setIsVisible(!isVisible)
        if (!isVisible) {
          refreshDebugInfo()
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isVisible])

  const refreshDebugInfo = async () => {
    try {
      // Get session info
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      
      // Get user info
      const { data: { user: supabaseUser }, error: userError } = await supabase.auth.getUser()
      
      // Get current user from our auth helper
      const currentUser = await auth.getCurrentUser()

      setSessionInfo({
        hasSession: !!session,
        sessionError: sessionError?.message,
        sessionExpiry: session?.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : null,
        accessToken: session?.access_token ? `${session.access_token.substring(0, 20)}...` : null,
        refreshToken: session?.refresh_token ? `${session.refresh_token.substring(0, 20)}...` : null,
      })

      setDebugInfo({
        contextUser: !!user,
        contextLoading: loading,
        supabaseUser: !!supabaseUser,
        supabaseUserError: userError?.message,
        currentUser: !!currentUser,
        userEmail: user?.email || supabaseUser?.email || 'None',
        userProfile: user?.profile || 'None',
        localStorage: typeof window !== 'undefined' ? Object.keys(localStorage).filter(k => k.includes('supabase')).length : 0,
        timestamp: new Date().toLocaleString()
      })
    } catch (error) {
      console.error('Error getting debug info:', error)
      setDebugInfo({ error: error.message })
    }
  }

  const handleClearAuth = async () => {
    try {
      // Clear auth cache
      auth.clearCache()
      
      // Clear localStorage
      if (typeof window !== 'undefined') {
        const keys = Object.keys(localStorage).filter(k => k.includes('supabase'))
        keys.forEach(key => localStorage.removeItem(key))
      }
      
      // Sign out
      await auth.signOut()
      
      // Refresh debug info
      setTimeout(refreshDebugInfo, 1000)
    } catch (error) {
      console.error('Error clearing auth:', error)
    }
  }

  const handleForceRefresh = async () => {
    try {
      // Force refresh the session
      const { data, error } = await supabase.auth.refreshSession()
      if (error) {
        console.error('Refresh error:', error)
      } else {
        console.log('Session refreshed successfully')
      }
      
      setTimeout(refreshDebugInfo, 1000)
    } catch (error) {
      console.error('Error refreshing session:', error)
    }
  }

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => {
            setIsVisible(true)
            refreshDebugInfo()
          }}
          variant="outline"
          size="sm"
          className="bg-white shadow-lg"
        >
          <Bug className="h-4 w-4" />
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96">
      <Card className="shadow-lg border-2 border-blue-200">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center justify-between">
            <span className="flex items-center">
              <Bug className="h-4 w-4 mr-2" />
              Auth Debug Panel
            </span>
            <Button
              onClick={() => setIsVisible(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-xs">
          {/* Status Indicators */}
          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center space-x-2">
              {loading ? (
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
              ) : user ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-500" />
              )}
              <span className="font-medium">
                {loading ? 'Loading' : user ? 'Authenticated' : 'Not Authenticated'}
              </span>
            </div>
            <Badge variant={sessionInfo?.hasSession ? 'default' : 'destructive'}>
              {sessionInfo?.hasSession ? 'Session Active' : 'No Session'}
            </Badge>
          </div>

          {/* Debug Info */}
          {debugInfo && (
            <div className="space-y-2 bg-gray-50 p-2 rounded">
              <div><strong>Context User:</strong> {debugInfo.contextUser ? '✓' : '✗'}</div>
              <div><strong>Context Loading:</strong> {debugInfo.contextLoading ? '✓' : '✗'}</div>
              <div><strong>Supabase User:</strong> {debugInfo.supabaseUser ? '✓' : '✗'}</div>
              <div><strong>Current User:</strong> {debugInfo.currentUser ? '✓' : '✗'}</div>
              <div><strong>Email:</strong> {debugInfo.userEmail}</div>
              <div><strong>Profile:</strong> {typeof debugInfo.userProfile === 'object' ? JSON.stringify(debugInfo.userProfile) : debugInfo.userProfile}</div>
              <div><strong>Storage Keys:</strong> {debugInfo.localStorage}</div>
              {debugInfo.error && (
                <div className="text-red-600"><strong>Error:</strong> {debugInfo.error}</div>
              )}
            </div>
          )}

          {/* Session Info */}
          {sessionInfo && (
            <div className="space-y-2 bg-blue-50 p-2 rounded">
              <div><strong>Session:</strong> {sessionInfo.hasSession ? '✓' : '✗'}</div>
              {sessionInfo.sessionExpiry && (
                <div><strong>Expires:</strong> {sessionInfo.sessionExpiry}</div>
              )}
              {sessionInfo.sessionError && (
                <div className="text-red-600"><strong>Session Error:</strong> {sessionInfo.sessionError}</div>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-2">
            <Button onClick={refreshDebugInfo} size="sm" variant="outline">
              <RefreshCw className="h-3 w-3 mr-1" />
              Refresh
            </Button>
            <Button onClick={handleForceRefresh} size="sm" variant="outline">
              Force Refresh
            </Button>
            <Button onClick={handleClearAuth} size="sm" variant="destructive">
              Clear Auth
            </Button>
          </div>

          <div className="text-xs text-gray-500 mt-2">
            Press Ctrl+Shift+D to toggle this panel
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
