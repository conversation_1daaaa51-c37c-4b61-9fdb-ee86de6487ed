import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()

  // Add security headers
  res.headers.set('X-Frame-Options', 'DENY')
  res.headers.set('X-Content-Type-Options', 'nosniff')
  res.headers.set('Referrer-Policy', 'origin-when-cross-origin')

  // Handle auth callback routes
  if (req.nextUrl.pathname.startsWith('/auth/callback')) {
    return res
  }

  // Handle share token routes (these should work without auth)
  if (req.nextUrl.pathname.startsWith('/share/')) {
    return res
  }

  // Handle API routes
  if (req.nextUrl.pathname.startsWith('/api/')) {
    return res
  }

  // For all other routes, let the client handle auth state
  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
