'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { auth, AuthUser } from '@/lib/auth'

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: any }>
  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: any }>
  signOut: () => Promise<void>
  updateProfile: (updates: { full_name?: string; email?: string }) => Promise<{ error: any }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [initialized, setInitialized] = useState(false)

  useEffect(() => {
    let mounted = true
    let retryCount = 0
    const maxRetries = 3

    // Timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      if (mounted && !initialized) {
        console.warn('Auth loading timeout - forcing loading to false')
        setLoading(false)
        setInitialized(true)
      }
    }, 8000) // Reduced to 8 seconds

    // Get initial user with retry logic
    const initializeAuth = async () => {
      while (retryCount < maxRetries && mounted && !initialized) {
        try {
          console.log(`🔐 Initializing auth (attempt ${retryCount + 1}/${maxRetries})`)

          // First check if we have a session
          const { data: { session }, error: sessionError } = await auth.getSession()

          if (sessionError) {
            console.error('Session error:', sessionError)
            throw sessionError
          }

          if (!session) {
            console.log('🔐 No session found')
            if (mounted) {
              setUser(null)
              setLoading(false)
              setInitialized(true)
              clearTimeout(loadingTimeout)
            }
            return
          }

          // Get full user data
          const user = await auth.getCurrentUser()
          console.log('🔐 Auth initialized successfully:', !!user)

          if (mounted) {
            setUser(user)
            setLoading(false)
            setInitialized(true)
            clearTimeout(loadingTimeout)
          }
          return
        } catch (error) {
          retryCount++
          console.error(`Auth initialization error (attempt ${retryCount}):`, error)

          if (retryCount >= maxRetries) {
            console.error('Max auth initialization retries reached')
            if (mounted) {
              setUser(null)
              setLoading(false)
              setInitialized(true)
              clearTimeout(loadingTimeout)
            }
            return
          }

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
        }
      }
    }

    initializeAuth()

    // Listen for auth changes with improved error handling
    const { data: { subscription } } = auth.onAuthStateChange((user) => {
      if (mounted) {
        console.log('🔐 Auth state changed:', !!user)
        setUser(user)
        if (!initialized) {
          setLoading(false)
          setInitialized(true)
          clearTimeout(loadingTimeout)
        }
      }
    })

    return () => {
      mounted = false
      clearTimeout(loadingTimeout)
      subscription.unsubscribe()
    }
  }, [])

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true)
      const { error } = await auth.signIn(email, password)

      if (!error) {
        // Don't set loading to false immediately - let the auth state change handle it
        console.log('🔐 Sign in successful, waiting for auth state change')
      } else {
        setLoading(false)
      }

      return { error }
    } catch (error) {
      console.error('Sign in error:', error)
      setLoading(false)
      return { error }
    }
  }

  const signUp = async (email: string, password: string, fullName?: string) => {
    try {
      setLoading(true)
      const { error } = await auth.signUp(email, password, fullName)
      setLoading(false)
      return { error }
    } catch (error) {
      console.error('Sign up error:', error)
      setLoading(false)
      return { error }
    }
  }

  const signOut = async () => {
    try {
      setLoading(true)
      await auth.signOut()
      setUser(null)
      setInitialized(false) // Reset initialization state
      setLoading(false)
    } catch (error) {
      console.error('Sign out error:', error)
      setLoading(false)
    }
  }

  const updateProfile = async (updates: { full_name?: string; email?: string }) => {
    const { error } = await auth.updateProfile(updates)
    if (!error && user) {
      // Filter out undefined values and convert them to null
      const profileUpdates: { full_name: string | null; email: string | null } = {
        full_name: updates.full_name !== undefined ? updates.full_name : user.profile?.full_name || null,
        email: updates.email !== undefined ? updates.email : user.profile?.email || null
      }

      setUser({
        ...user,
        profile: profileUpdates,
        email: user.email
      })
    }
    return { error }
  }

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
