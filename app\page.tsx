'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/auth/auth-provider';
import { LoginForm } from '@/components/auth/login-form';
import { petStorage } from '@/lib/storage';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Heart,
  Calendar,
  Clock,
  Utensils,
  Trash2,
  Syringe,
  StickyNote,
  PawPrint,
  LogOut
} from 'lucide-react';
import { FeedingTracker } from '@/components/feeding-tracker';
import { LitterTracker } from '@/components/litter-tracker';
import { VaccinationTracker } from '@/components/vaccination-tracker';
import { NotesTracker } from '@/components/notes-tracker';
import { PetManager } from '@/components/pet-manager';
import { PetAvatar } from '@/components/pet-avatar';
import { LoadingDebug } from '@/components/debug/loading-debug';
import { AuthDebug } from '@/components/debug/auth-debug';

interface Activity {
  id: string;
  type: 'feeding' | 'litter' | 'vaccination' | 'note';
  title: string;
  timestamp: string;
  details?: string;
}

export default function Home() {
  const { user, loading, signOut } = useAuth();
  const [activities, setActivities] = useState<Activity[]>([]);
  const [selectedPetId, setSelectedPetId] = useState<string | null>(null);
  const [selectedPet, setSelectedPet] = useState<any>(null);
  const [todayMealsCount, setTodayMealsCount] = useState(0);
  const [todayLitterCount, setTodayLitterCount] = useState(0);
  const [vaccinationsCount, setVaccinationsCount] = useState(0);
  const [notesCount, setNotesCount] = useState(0);
  const [weeklyMealsAvg, setWeeklyMealsAvg] = useState(0);
  const [lastFeedingTime, setLastFeedingTime] = useState<string | null>(null);
  const [upcomingVaccinations, setUpcomingVaccinations] = useState<any[]>([]);
  const [healthAlerts, setHealthAlerts] = useState<string[]>([]);

  // Debug logging for loading states
  useEffect(() => {
    console.log('🔍 Auth state changed:', { user: !!user, loading, userEmail: user?.email })
  }, [user, loading])

  // Emergency cache clear shortcut (Ctrl+Shift+C)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'C') {
        event.preventDefault()
        console.log('🧹 Emergency cache clear triggered')
        import('@/lib/cache-utils').then(({ cacheUtils }) => {
          cacheUtils.clearCacheAndReload()
        })
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [])

  const updateData = async () => {
    if (!selectedPetId) {
      setActivities([]);
      setTodayMealsCount(0);
      setTodayLitterCount(0);
      setVaccinationsCount(0);
      setNotesCount(0);
      return;
    }

    try {
      const [feeding, litter, vaccinations, notes] = await Promise.all([
        petStorage.getFeedingLogs(selectedPetId),
        petStorage.getLitterLogs(selectedPetId),
        petStorage.getVaccinations(selectedPetId),
        petStorage.getNotes(selectedPetId)
      ]);

      // Calculate today's counts
      const today = new Date().toDateString();
      const todayFeeding = feeding.filter(f =>
        new Date(f.timestamp).toDateString() === today
      );
      const todayLitter = litter.filter(l =>
        new Date(l.timestamp).toDateString() === today
      );

      setTodayMealsCount(todayFeeding.length);
      setTodayLitterCount(todayLitter.length);
      setVaccinationsCount(vaccinations.length);
      setNotesCount(notes.length);

      // Calculate weekly feeding average
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      const weeklyFeeding = feeding.filter(f => new Date(f.timestamp) >= oneWeekAgo);
      setWeeklyMealsAvg(Math.round((weeklyFeeding.length / 7) * 10) / 10);

      // Get last feeding time
      if (feeding.length > 0) {
        const sortedFeeding = feeding.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
        setLastFeedingTime(sortedFeeding[0].timestamp);
      } else {
        setLastFeedingTime(null);
      }

      // Get upcoming vaccinations (next 30 days)
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      const upcoming = vaccinations.filter(v =>
        v.next_due &&
        new Date(v.next_due) <= thirtyDaysFromNow &&
        new Date(v.next_due) >= new Date()
      );
      setUpcomingVaccinations(upcoming);

      // Generate health alerts
      const alerts: string[] = [];

      // Check if no feeding today
      if (todayFeeding.length === 0) {
        alerts.push("No feeding recorded today");
      }

      // Check for overdue vaccinations
      const overdue = vaccinations.filter(v =>
        v.next_due && new Date(v.next_due) < new Date()
      );
      if (overdue.length > 0) {
        alerts.push(`${overdue.length} vaccination(s) overdue`);
      }

      // Check if last feeding was more than 12 hours ago
      if (feeding.length > 0) {
        const lastFeeding = new Date(feeding.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0].timestamp);
        const hoursAgo = (new Date().getTime() - lastFeeding.getTime()) / (1000 * 60 * 60);
        if (hoursAgo > 12) {
          alerts.push("Last feeding was more than 12 hours ago");
        }
      }

      setHealthAlerts(alerts);

      const allActivities: Activity[] = [
        ...feeding.map(f => ({
          id: f.id,
          type: 'feeding' as const,
          title: `Fed ${f.food_type}`,
          timestamp: f.timestamp,
          details: f.amount || ''
        })),
        ...litter.map(l => ({
          id: l.id,
          type: 'litter' as const,
          title: `Litter ${l.type}`,
          timestamp: l.timestamp,
          details: l.notes || ''
        })),
        ...vaccinations.map(v => ({
          id: v.id,
          type: 'vaccination' as const,
          title: `${v.vaccine} vaccine`,
          timestamp: v.date,
          details: v.veterinarian
        })),
        ...notes.map(n => ({
          id: n.id,
          type: 'note' as const,
          title: n.topic,
          timestamp: n.timestamp,
          details: n.description.substring(0, 100) + (n.description.length > 100 ? '...' : '')
        }))
      ];

      // Sort by timestamp (most recent first)
      allActivities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      setActivities(allActivities.slice(0, 10)); // Show only last 10 activities
    } catch (error) {
      console.error('Error updating data:', error);
    }
  };

  useEffect(() => {
    updateData();
  }, [selectedPetId]);

  // Show login form if not authenticated
  if (loading) {
    return (
      <>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <PawPrint className="h-12 w-12 mx-auto mb-4 animate-pulse" />
            <p>Loading...</p>
          </div>
        </div>
        <LoadingDebug
          loading={loading}
          user={user}
          onRetry={() => window.location.reload()}
        />
      </>
    );
  }

  if (!user) {
    return <LoginForm />;
  }

  const handlePetSelect = async (petId: string | null) => {
    setSelectedPetId(petId);
    if (petId) {
      // Get pet data for display
      const pets = await petStorage.getPets();
      const selectedPetData = pets.find(p => p.id === petId);
      setSelectedPet(selectedPetData || null);
    } else {
      setSelectedPet(null);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'feeding': return <Utensils className="h-4 w-4" />;
      case 'litter': return <Trash2 className="h-4 w-4" />;
      case 'vaccination': return <Syringe className="h-4 w-4" />;
      case 'note': return <StickyNote className="h-4 w-4" />;
      default: return <Heart className="h-4 w-4" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'feeding': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'litter': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'vaccination': return 'bg-green-100 text-green-800 border-green-200';
      case 'note': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            {selectedPet ? (
              <PetAvatar
                imageUrl={selectedPet.image_url}
                petName={selectedPet.name}
                size="xl"
                className="shadow-lg"
              />
            ) : (
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 p-4 rounded-xl shadow-lg">
                <PawPrint className="h-8 w-8 text-white" />
              </div>
            )}
            <div>
              <h1 className="text-3xl font-bold text-gray-900">FurFormance Pet Tracker</h1>
              <p className="text-gray-600">
                Caring for {selectedPet ? selectedPet.name : 'Select a Pet'}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-500">Welcome, {user.profile?.full_name || user.email}</p>
              <p className="text-lg font-semibold text-gray-900">
                {new Date().toLocaleDateString([], {
                  weekday: 'long',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
            </div>
            <Button variant="outline" onClick={signOut}>
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </div>

        <Tabs defaultValue="pets" className="w-full">
          <TabsList className="grid w-full grid-cols-6 mb-8">
            <TabsTrigger value="pets" className="flex items-center space-x-2">
              <PawPrint className="h-4 w-4" />
              <span className="hidden sm:inline">My Pets</span>
            </TabsTrigger>
            <TabsTrigger value="dashboard" className="flex items-center space-x-2">
              <Heart className="h-4 w-4" />
              <span className="hidden sm:inline">Dashboard</span>
            </TabsTrigger>
            <TabsTrigger value="feeding" className="flex items-center space-x-2">
              <Utensils className="h-4 w-4" />
              <span className="hidden sm:inline">Feeding</span>
            </TabsTrigger>
            <TabsTrigger value="litter" className="flex items-center space-x-2">
              <Trash2 className="h-4 w-4" />
              <span className="hidden sm:inline">Litter</span>
            </TabsTrigger>
            <TabsTrigger value="vaccination" className="flex items-center space-x-2">
              <Syringe className="h-4 w-4" />
              <span className="hidden sm:inline">Health</span>
            </TabsTrigger>
            <TabsTrigger value="notes" className="flex items-center space-x-2">
              <StickyNote className="h-4 w-4" />
              <span className="hidden sm:inline">Notes</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pets" className="space-y-6">
            <PetManager
              selectedPetId={selectedPetId}
              onPetSelect={handlePetSelect}
              onPetUpdate={updateData}
            />
          </TabsContent>

          <TabsContent value="dashboard" className="space-y-6">
            {!selectedPetId ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <PawPrint className="h-16 w-16 text-gray-400 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Pet Selected</h3>
                  <p className="text-gray-600 text-center mb-4">
                    Please select a pet from the &quot;My Pets&quot; tab to view the dashboard.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <>
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card className="hover:shadow-md transition-shadow duration-200">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="bg-orange-100 p-3 rounded-lg">
                        <Utensils className="h-6 w-6 text-orange-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Today&apos;s Meals</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {todayMealsCount}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow duration-200">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="bg-blue-100 p-3 rounded-lg">
                        <Trash2 className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Litter Cleaned</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {todayLitterCount}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow duration-200">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="bg-green-100 p-3 rounded-lg">
                        <Syringe className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Vaccinations</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {vaccinationsCount}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow duration-200">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="bg-purple-100 p-3 rounded-lg">
                        <StickyNote className="h-6 w-6 text-purple-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Notes</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {notesCount}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Health Alerts */}
              {healthAlerts.length > 0 && (
                <Card className="border-orange-200 bg-orange-50">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-orange-800">
                      <Calendar className="h-5 w-5" />
                      <span>Health Alerts</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {healthAlerts.map((alert, index) => (
                        <div key={index} className="flex items-center space-x-2 text-orange-700">
                          <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                          <span className="text-sm">{alert}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Enhanced Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Weekly Average</CardTitle>
                    <CardDescription>Meals per day</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-blue-600">
                      {weeklyMealsAvg}
                    </div>
                    <p className="text-sm text-gray-600 mt-2">
                      Based on last 7 days
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Last Feeding</CardTitle>
                    <CardDescription>Most recent meal</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-lg font-semibold text-green-600">
                      {lastFeedingTime ? (
                        <>
                          {(() => {
                            const hours = Math.floor((new Date().getTime() - new Date(lastFeedingTime).getTime()) / (1000 * 60 * 60));
                            return hours < 1 ? 'Less than 1 hour ago' : `${hours} hour${hours > 1 ? 's' : ''} ago`;
                          })()}
                        </>
                      ) : (
                        'No feeding recorded'
                      )}
                    </div>
                    {lastFeedingTime && (
                      <p className="text-sm text-gray-600 mt-2">
                        {new Date(lastFeedingTime).toLocaleString()}
                      </p>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Upcoming</CardTitle>
                    <CardDescription>Vaccinations due</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-purple-600">
                      {upcomingVaccinations.length}
                    </div>
                    <p className="text-sm text-gray-600 mt-2">
                      Next 30 days
                    </p>
                    {upcomingVaccinations.length > 0 && (
                      <div className="mt-3 space-y-1">
                        {upcomingVaccinations.slice(0, 2).map((vacc) => (
                          <div key={vacc.id} className="text-xs text-gray-600">
                            {vacc.vaccine} - {new Date(vacc.next_due).toLocaleDateString()}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Clock className="h-5 w-5" />
                    <span>Recent Activity</span>
                  </CardTitle>
                  <CardDescription>
                    Latest updates for {selectedPet ? selectedPet.name : 'your pets'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {activities.length === 0 ? (
                      <p className="text-gray-500 text-center py-4">No recent activity</p>
                    ) : (
                      activities.map((activity) => (
                        <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                          <Badge variant="outline" className={getActivityColor(activity.type)}>
                            {getActivityIcon(activity.type)}
                          </Badge>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                            {activity.details && (
                              <p className="text-sm text-gray-600 truncate">{activity.details}</p>
                            )}
                            <p className="text-xs text-gray-500 mt-1">{formatTimestamp(activity.timestamp)}</p>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
            </>
            )}
          </TabsContent>

          <TabsContent value="feeding">
            {selectedPetId ? (
              <FeedingTracker petId={selectedPetId} onUpdate={updateData} />
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Utensils className="h-16 w-16 text-gray-400 mb-4" />
                  <p className="text-gray-600">Please select a pet to track feeding.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="litter">
            {selectedPetId ? (
              <LitterTracker petId={selectedPetId} onUpdate={updateData} />
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Trash2 className="h-16 w-16 text-gray-400 mb-4" />
                  <p className="text-gray-600">Please select a pet to track litter.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="vaccination">
            {selectedPetId ? (
              <VaccinationTracker petId={selectedPetId} onUpdate={updateData} />
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Syringe className="h-16 w-16 text-gray-400 mb-4" />
                  <p className="text-gray-600">Please select a pet to track vaccinations.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="notes">
            {selectedPetId ? (
              <NotesTracker petId={selectedPetId} onUpdate={updateData} />
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <StickyNote className="h-16 w-16 text-gray-400 mb-4" />
                  <p className="text-gray-600">Please select a pet to add notes.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Auth Debug Panel - only in development */}
      {process.env.NODE_ENV === 'development' && <AuthDebug />}
    </div>
  );
}